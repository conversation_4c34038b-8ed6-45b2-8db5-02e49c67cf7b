// Simple API test to check if the server is responding
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

async function testAPI() {
  console.log('🔍 Testing API Server Connection...');
  
  try {
    // Test basic connectivity
    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength('{}')
      }
    }, '{}');

    console.log(`📊 Status Code: ${response.statusCode}`);
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));
    
    if (response.statusCode === 400) {
      console.log('✅ API Server is responding (400 is expected for empty login request)');
    } else {
      console.log('⚠️  Unexpected response, but server is responding');
    }
    
  } catch (error) {
    console.error('❌ API Server connection failed:', error.message);
  }
}

// Helper function to make API requests
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

testAPI();
