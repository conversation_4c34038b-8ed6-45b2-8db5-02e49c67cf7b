-- =============================================
-- Client Reminders Database Schema
-- CA Portal - Multi-tenant Client Reminder System
-- Database: CA_Portal_kumar_associates
-- =============================================

USE [CA_Portal_kumar_associates];

PRINT '============================================================';
PRINT 'Creating Client Reminders Schema';
PRINT '============================================================';

-- =============================================
-- 1. ClientReminders Table - Main reminder records
-- =============================================
PRINT '';
PRINT '1. CREATING CLIENTREMINDERS TABLE';
PRINT '----------------------------------';

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ClientReminders' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ClientReminders] (
        [ReminderId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ReminderType] NVARCHAR(50) NOT NULL, -- WhatsApp, Email, Both
        [TaxType] NVARCHAR(100) NOT NULL, -- GST Return, TDS Return, ITR Filing, etc.
        [DueDate] DATETIME NOT NULL,
        [ClientGroup] NVARCHAR(100) NOT NULL, -- All, Corporate, Individual, MSME, High-Value
        [CustomMessage] NVARCHAR(MAX) NULL,
        [Status] NVARCHAR(50) NOT NULL DEFAULT 'Scheduled', -- Scheduled, Sent, Failed, Cancelled
        [ClientCount] INT NOT NULL DEFAULT 0,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NOT NULL,
        [ScheduledAt] DATETIME NULL, -- When to send the reminder
        [SentAt] DATETIME NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [Notes] NVARCHAR(MAX) NULL,
        [Priority] NVARCHAR(20) NOT NULL DEFAULT 'Medium', -- Low, Medium, High, Critical
        [RetryCount] INT NOT NULL DEFAULT 0,
        [LastRetryAt] DATETIME NULL,
        [UpdatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedBy] UNIQUEIDENTIFIER NULL,
        
        -- Multi-tenant support
        [TenantId] UNIQUEIDENTIFIER NOT NULL,
        
        CONSTRAINT [CK_ClientReminders_Type] CHECK ([ReminderType] IN ('WhatsApp', 'Email', 'Both')),
        CONSTRAINT [CK_ClientReminders_Status] CHECK ([Status] IN ('Scheduled', 'Sent', 'Failed', 'Cancelled', 'Processing')),
        CONSTRAINT [CK_ClientReminders_Group] CHECK ([ClientGroup] IN ('All', 'Corporate', 'Individual', 'MSME', 'High-Value')),
        CONSTRAINT [CK_ClientReminders_Priority] CHECK ([Priority] IN ('Low', 'Medium', 'High', 'Critical')),
        CONSTRAINT [FK_ClientReminders_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId])
    );
    
    -- Create indexes for performance
    CREATE INDEX [IX_ClientReminders_Status] ON [ClientReminders]([Status], [IsActive]);
    CREATE INDEX [IX_ClientReminders_DueDate] ON [ClientReminders]([DueDate], [Status]);
    CREATE INDEX [IX_ClientReminders_CreatedAt] ON [ClientReminders]([CreatedAt] DESC);
    CREATE INDEX [IX_ClientReminders_TenantId] ON [ClientReminders]([TenantId], [IsActive]);
    CREATE INDEX [IX_ClientReminders_ScheduledAt] ON [ClientReminders]([ScheduledAt], [Status]) WHERE [ScheduledAt] IS NOT NULL;
    
    PRINT '✅ ClientReminders table created with indexes';
END
ELSE
BEGIN
    PRINT '⚠️  ClientReminders table already exists';
END

-- =============================================
-- 2. ClientReminderDelivery Table - Individual delivery tracking
-- =============================================
PRINT '';
PRINT '2. CREATING CLIENTREMINDERDELIVERY TABLE';
PRINT '-----------------------------------------';

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ClientReminderDelivery' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ClientReminderDelivery] (
        [DeliveryId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [ReminderId] UNIQUEIDENTIFIER NOT NULL,
        [ClientId] UNIQUEIDENTIFIER NOT NULL,
        [DeliveryType] NVARCHAR(50) NOT NULL, -- WhatsApp, Email
        [DeliveryStatus] NVARCHAR(50) NOT NULL DEFAULT 'Pending', -- Pending, Sent, Failed, Bounced
        [SentAt] DATETIME NULL,
        [DeliveryResponse] NVARCHAR(MAX) NULL, -- API response or error details
        [RetryCount] INT NOT NULL DEFAULT 0,
        [LastRetryAt] DATETIME NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        
        -- Contact information at time of sending
        [ContactEmail] NVARCHAR(255) NULL,
        [ContactPhone] NVARCHAR(50) NULL,
        [ContactName] NVARCHAR(255) NULL,
        
        CONSTRAINT [FK_ClientReminderDelivery_Reminder] FOREIGN KEY ([ReminderId]) REFERENCES [ClientReminders]([ReminderId]) ON DELETE CASCADE,
        CONSTRAINT [FK_ClientReminderDelivery_Client] FOREIGN KEY ([ClientId]) REFERENCES [Clients]([ClientId]),
        CONSTRAINT [CK_ClientReminderDelivery_Type] CHECK ([DeliveryType] IN ('WhatsApp', 'Email')),
        CONSTRAINT [CK_ClientReminderDelivery_Status] CHECK ([DeliveryStatus] IN ('Pending', 'Sent', 'Failed', 'Bounced', 'Delivered', 'Read'))
    );
    
    -- Create indexes for performance
    CREATE INDEX [IX_ClientReminderDelivery_ReminderId] ON [ClientReminderDelivery]([ReminderId], [DeliveryStatus]);
    CREATE INDEX [IX_ClientReminderDelivery_ClientId] ON [ClientReminderDelivery]([ClientId], [CreatedAt] DESC);
    CREATE INDEX [IX_ClientReminderDelivery_Status] ON [ClientReminderDelivery]([DeliveryStatus], [SentAt]);
    CREATE INDEX [IX_ClientReminderDelivery_Retry] ON [ClientReminderDelivery]([RetryCount], [LastRetryAt]) WHERE [DeliveryStatus] = 'Failed';
    
    PRINT '✅ ClientReminderDelivery table created with indexes';
END
ELSE
BEGIN
    PRINT '⚠️  ClientReminderDelivery table already exists';
END

-- =============================================
-- 3. ClientGroups Table - Client grouping management
-- =============================================
PRINT '';
PRINT '3. CREATING CLIENTGROUPS TABLE';
PRINT '-------------------------------';

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ClientGroups' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ClientGroups] (
        [GroupId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [GroupName] NVARCHAR(100) NOT NULL,
        [GroupType] NVARCHAR(50) NOT NULL DEFAULT 'Static', -- Static, Dynamic
        [FilterCriteria] NVARCHAR(MAX) NULL, -- JSON criteria for dynamic groups
        [Description] NVARCHAR(500) NULL,
        [CreatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [CreatedBy] UNIQUEIDENTIFIER NOT NULL,
        [UpdatedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [UpdatedBy] UNIQUEIDENTIFIER NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        [TenantId] UNIQUEIDENTIFIER NOT NULL,
        
        CONSTRAINT [FK_ClientGroups_CreatedBy] FOREIGN KEY ([CreatedBy]) REFERENCES [Users]([UserId]),
        CONSTRAINT [CK_ClientGroups_Type] CHECK ([GroupType] IN ('Static', 'Dynamic')),
        CONSTRAINT [UQ_ClientGroups_Name_Tenant] UNIQUE ([GroupName], [TenantId], [IsActive])
    );
    
    -- Create indexes
    CREATE INDEX [IX_ClientGroups_TenantId] ON [ClientGroups]([TenantId], [IsActive]);
    CREATE INDEX [IX_ClientGroups_Type] ON [ClientGroups]([GroupType], [IsActive]);
    
    PRINT '✅ ClientGroups table created with indexes';
END
ELSE
BEGIN
    PRINT '⚠️  ClientGroups table already exists';
END

-- =============================================
-- 4. ClientGroupMembers Table - Group membership
-- =============================================
PRINT '';
PRINT '4. CREATING CLIENTGROUPMEMBERS TABLE';
PRINT '------------------------------------';

IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ClientGroupMembers' AND xtype='U')
BEGIN
    CREATE TABLE [dbo].[ClientGroupMembers] (
        [MembershipId] UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        [GroupId] UNIQUEIDENTIFIER NOT NULL,
        [ClientId] UNIQUEIDENTIFIER NOT NULL,
        [AddedAt] DATETIME NOT NULL DEFAULT GETUTCDATE(),
        [AddedBy] UNIQUEIDENTIFIER NOT NULL,
        [IsActive] BIT NOT NULL DEFAULT 1,
        
        CONSTRAINT [FK_ClientGroupMembers_Group] FOREIGN KEY ([GroupId]) REFERENCES [ClientGroups]([GroupId]) ON DELETE CASCADE,
        CONSTRAINT [FK_ClientGroupMembers_Client] FOREIGN KEY ([ClientId]) REFERENCES [Clients]([ClientId]),
        CONSTRAINT [FK_ClientGroupMembers_AddedBy] FOREIGN KEY ([AddedBy]) REFERENCES [Users]([UserId]),
        CONSTRAINT [UQ_ClientGroupMembers_Group_Client] UNIQUE ([GroupId], [ClientId])
    );
    
    -- Create indexes
    CREATE INDEX [IX_ClientGroupMembers_GroupId] ON [ClientGroupMembers]([GroupId], [IsActive]);
    CREATE INDEX [IX_ClientGroupMembers_ClientId] ON [ClientGroupMembers]([ClientId], [IsActive]);
    
    PRINT '✅ ClientGroupMembers table created with indexes';
END
ELSE
BEGIN
    PRINT '⚠️  ClientGroupMembers table already exists';
END

-- =============================================
-- 5. Insert Default Client Groups
-- =============================================
PRINT '';
PRINT '5. INSERTING DEFAULT CLIENT GROUPS';
PRINT '-----------------------------------';

-- Get a default user ID for system operations (first admin user)
DECLARE @SystemUserId UNIQUEIDENTIFIER;
SELECT TOP 1 @SystemUserId = UserId FROM Users WHERE Role = 'Admin' AND IsActive = 1;

IF @SystemUserId IS NOT NULL
BEGIN
    -- Insert default client groups if they don't exist
    IF NOT EXISTS (SELECT 1 FROM ClientGroups WHERE GroupName = 'All Clients' AND IsActive = 1)
    BEGIN
        INSERT INTO ClientGroups (GroupName, GroupType, Description, CreatedBy, TenantId)
        VALUES ('All Clients', 'Dynamic', 'All active clients in the system', @SystemUserId, 
                (SELECT TOP 1 OrganizationId FROM TenantRegistry.dbo.Organizations WHERE TenantKey = 'kumar-associates'));
        PRINT '✅ Default group "All Clients" created';
    END
    
    IF NOT EXISTS (SELECT 1 FROM ClientGroups WHERE GroupName = 'Corporate Clients' AND IsActive = 1)
    BEGIN
        INSERT INTO ClientGroups (GroupName, GroupType, Description, CreatedBy, TenantId)
        VALUES ('Corporate Clients', 'Dynamic', 'Corporate and company clients', @SystemUserId,
                (SELECT TOP 1 OrganizationId FROM TenantRegistry.dbo.Organizations WHERE TenantKey = 'kumar-associates'));
        PRINT '✅ Default group "Corporate Clients" created';
    END
    
    IF NOT EXISTS (SELECT 1 FROM ClientGroups WHERE GroupName = 'Individual Clients' AND IsActive = 1)
    BEGIN
        INSERT INTO ClientGroups (GroupName, GroupType, Description, CreatedBy, TenantId)
        VALUES ('Individual Clients', 'Dynamic', 'Individual and personal clients', @SystemUserId,
                (SELECT TOP 1 OrganizationId FROM TenantRegistry.dbo.Organizations WHERE TenantKey = 'kumar-associates'));
        PRINT '✅ Default group "Individual Clients" created';
    END
    
    IF NOT EXISTS (SELECT 1 FROM ClientGroups WHERE GroupName = 'MSME Clients' AND IsActive = 1)
    BEGIN
        INSERT INTO ClientGroups (GroupName, GroupType, Description, CreatedBy, TenantId)
        VALUES ('MSME Clients', 'Dynamic', 'Micro, Small and Medium Enterprise clients', @SystemUserId,
                (SELECT TOP 1 OrganizationId FROM TenantRegistry.dbo.Organizations WHERE TenantKey = 'kumar-associates'));
        PRINT '✅ Default group "MSME Clients" created';
    END
    
    IF NOT EXISTS (SELECT 1 FROM ClientGroups WHERE GroupName = 'High-Value Clients' AND IsActive = 1)
    BEGIN
        INSERT INTO ClientGroups (GroupName, GroupType, Description, CreatedBy, TenantId)
        VALUES ('High-Value Clients', 'Dynamic', 'High-value and premium clients', @SystemUserId,
                (SELECT TOP 1 OrganizationId FROM TenantRegistry.dbo.Organizations WHERE TenantKey = 'kumar-associates'));
        PRINT '✅ Default group "High-Value Clients" created';
    END
END
ELSE
BEGIN
    PRINT '⚠️  No admin user found - skipping default groups creation';
END

PRINT '';
PRINT '============================================================';
PRINT 'Client Reminders Schema Creation Complete!';
PRINT 'Next: Run client-reminders-procedures.sql for stored procedures';
PRINT '============================================================';
