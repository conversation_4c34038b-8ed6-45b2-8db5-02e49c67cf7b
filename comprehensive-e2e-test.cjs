// Comprehensive End-to-End Testing for Compliance Calendar Module
const https = require('https');

// Disable SSL certificate validation for localhost testing
process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;

// Test configuration
const API_BASE = 'https://localhost:7000/api';
const FRONTEND_BASE = 'http://localhost:8081';

// Test data
const TEST_USER = {
  email: "<EMAIL>",
  password: "password123",
  tenantKey: "kumar-associates"
};

let authToken = null;
let testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Helper function to make API requests
function makeRequest(options, postData = null) {
  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({ statusCode: res.statusCode, data: response });
        } catch (error) {
          resolve({ statusCode: res.statusCode, data: data });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (postData) {
      req.write(postData);
    }
    req.end();
  });
}

// Test logging function
function logTest(testName, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${testName}`);
  if (details) {
    console.log(`   ${details}`);
  }
  
  testResults.tests.push({ name: testName, passed, details });
  if (passed) {
    testResults.passed++;
  } else {
    testResults.failed++;
  }
}

// Authentication test
async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');

  try {
    const loginPayload = JSON.stringify(TEST_USER);
    console.log('📤 Sending login request:', loginPayload);

    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(loginPayload)
      }
    }, loginPayload);

    console.log(`📊 Login Status Code: ${response.statusCode}`);
    console.log('📥 Login Response:', JSON.stringify(response.data, null, 2));

    if (response.statusCode === 200 && response.data.success && response.data.data && response.data.data.token) {
      authToken = response.data.data.token;
      logTest('User Authentication', true, `Token received: ${authToken.substring(0, 20)}...`);
      return true;
    } else {
      logTest('User Authentication', false, `Status: ${response.statusCode}, Response: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    logTest('User Authentication', false, `Error: ${error.message}`);
    return false;
  }
}

// Test compliance stats API
async function testComplianceStats() {
  console.log('\n📊 Testing Compliance Stats API...');
  
  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/compliance/stats',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.statusCode === 200 && response.data.success) {
      const stats = response.data.data;
      const hasRequiredFields = stats.hasOwnProperty('totalItems') && 
                               stats.hasOwnProperty('completedItems') && 
                               stats.hasOwnProperty('pendingItems');
      
      logTest('Compliance Stats API', hasRequiredFields, 
        `Total: ${stats.totalItems}, Completed: ${stats.completedItems}, Pending: ${stats.pendingItems}`);
      return hasRequiredFields;
    } else {
      logTest('Compliance Stats API', false, `Status: ${response.statusCode}`);
      return false;
    }
  } catch (error) {
    logTest('Compliance Stats API', false, `Error: ${error.message}`);
    return false;
  }
}

// Test compliance list API
async function testComplianceList() {
  console.log('\n📋 Testing Compliance List API...');

  try {
    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/compliance?pageNumber=1&pageSize=20',
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.statusCode === 200 && response.data.success) {
      const items = response.data.data;
      // Check if it's an array or has items property
      const itemsArray = Array.isArray(items) ? items : (items.items || []);
      const hasValidStructure = itemsArray.length === 0 || (
        itemsArray[0].hasOwnProperty('complianceId') &&
        itemsArray[0].hasOwnProperty('complianceType') &&
        itemsArray[0].hasOwnProperty('status') &&
        itemsArray[0].hasOwnProperty('dueDate')
      );

      logTest('Compliance List API', hasValidStructure, `Retrieved ${itemsArray.length} compliance items`);
      return { success: hasValidStructure, data: itemsArray };
    } else {
      logTest('Compliance List API', false, `Status: ${response.statusCode}, Response: ${JSON.stringify(response.data)}`);
      return { success: false, data: [] };
    }
  } catch (error) {
    logTest('Compliance List API', false, `Error: ${error.message}`);
    return { success: false, data: [] };
  }
}

// Test clients API
async function testClientsAPI() {
  console.log('\n👥 Testing Clients API...');

  try {
    const queryParams = new URLSearchParams({
      pageNumber: '1',
      pageSize: '100',
      isActive: 'true'
    });

    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: `/api/clients?${queryParams.toString()}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.statusCode === 200 && response.data.success) {
      const result = response.data.data;
      const clients = result.clients || result || [];
      const hasValidStructure = clients.length === 0 || (
        clients[0].hasOwnProperty('clientId') &&
        clients[0].hasOwnProperty('companyName')
      );

      logTest('Clients API', hasValidStructure, `Retrieved ${clients.length} clients`);
      return { success: hasValidStructure, data: clients };
    } else {
      logTest('Clients API', false, `Status: ${response.statusCode}, Response: ${JSON.stringify(response.data)}`);
      return { success: false, data: [] };
    }
  } catch (error) {
    logTest('Clients API', false, `Error: ${error.message}`);
    return { success: false, data: [] };
  }
}

// Test users API
async function testUsersAPI() {
  console.log('\n👤 Testing Users API...');

  try {
    const queryParams = new URLSearchParams({
      pageNumber: '1',
      pageSize: '100',
      isActive: 'true'
    });

    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: `/api/users?${queryParams.toString()}`,
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.statusCode === 200 && response.data.success) {
      const result = response.data.data;
      const users = result.users || result || [];
      const hasValidStructure = users.length === 0 || (
        users[0].hasOwnProperty('userId') &&
        users[0].hasOwnProperty('firstName') &&
        users[0].hasOwnProperty('lastName')
      );

      logTest('Users API', hasValidStructure, `Retrieved ${users.length} users`);
      return { success: hasValidStructure, data: users };
    } else {
      logTest('Users API', false, `Status: ${response.statusCode}, Response: ${JSON.stringify(response.data)}`);
      return { success: false, data: [] };
    }
  } catch (error) {
    logTest('Users API', false, `Error: ${error.message}`);
    return { success: false, data: [] };
  }
}

// Test compliance create API
async function testComplianceCreate(clientsData, usersData) {
  console.log('\n➕ Testing Compliance Create API...');

  if (clientsData.length === 0 || usersData.length === 0) {
    logTest('Compliance Create API', false, 'No clients or users available for testing');
    return { success: false, data: null };
  }

  try {
    const testClient = clientsData[0];
    const testUser = usersData[0];

    const createPayload = JSON.stringify({
      clientId: testClient.clientId,
      complianceType: "E2E Test Compliance",
      subType: "API Integration Test",
      description: "Testing compliance creation via E2E test",
      dueDate: "2025-07-01T23:59:59Z",
      priority: "Medium",
      assignedTo: testUser.userId,
      notes: "Created during comprehensive E2E testing"
    });

    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: '/api/compliance',
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(createPayload)
      }
    }, createPayload);

    if (response.statusCode === 201 && response.data.success) {
      const createdItem = response.data.data;
      const hasValidStructure = createdItem.hasOwnProperty('complianceId') &&
                               createdItem.hasOwnProperty('complianceType');

      logTest('Compliance Create API', hasValidStructure, `Created compliance item: ${createdItem.complianceId}`);
      return { success: hasValidStructure, data: createdItem };
    } else {
      logTest('Compliance Create API', false, `Status: ${response.statusCode}, Response: ${JSON.stringify(response.data)}`);
      return { success: false, data: null };
    }
  } catch (error) {
    logTest('Compliance Create API', false, `Error: ${error.message}`);
    return { success: false, data: null };
  }
}

// Test compliance completion API
async function testComplianceCompletion(complianceItem) {
  console.log('\n✅ Testing Compliance Completion API...');

  if (!complianceItem) {
    logTest('Compliance Completion API', false, 'No compliance item available for testing');
    return false;
  }

  try {
    const completePayload = JSON.stringify({
      notes: "Completed during E2E testing - this validates the completion workflow"
    });

    const response = await makeRequest({
      hostname: 'localhost',
      port: 7000,
      path: `/api/compliance/${complianceItem.complianceId}/complete`,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(completePayload)
      }
    }, completePayload);

    if (response.statusCode === 200 && response.data.success) {
      logTest('Compliance Completion API', true, `Successfully completed compliance item: ${complianceItem.complianceId}`);
      return true;
    } else {
      logTest('Compliance Completion API', false, `Status: ${response.statusCode}, Response: ${JSON.stringify(response.data)}`);
      return false;
    }
  } catch (error) {
    logTest('Compliance Completion API', false, `Error: ${error.message}`);
    return false;
  }
}

// Main test execution
async function runComprehensiveTests() {
  console.log('🧪 Starting Comprehensive E2E Testing for Compliance Calendar Module');
  console.log('=' .repeat(80));

  // Test 1: Authentication
  const authSuccess = await testAuthentication();
  if (!authSuccess) {
    console.log('\n❌ Authentication failed. Cannot proceed with other tests.');
    return;
  }

  // Test 2: API Integration Tests
  await testComplianceStats();
  const complianceResult = await testComplianceList();
  const clientsResult = await testClientsAPI();
  const usersResult = await testUsersAPI();

  // Test 3: CRUD Operations
  let createdComplianceItem = null;
  if (clientsResult.success && usersResult.success) {
    const createResult = await testComplianceCreate(clientsResult.data, usersResult.data);
    if (createResult.success) {
      createdComplianceItem = createResult.data;
    }
  }

  // Test 4: Completion Workflow
  if (createdComplianceItem) {
    await testComplianceCompletion(createdComplianceItem);
  }

  // Test Summary
  console.log('\n' + '='.repeat(80));
  console.log('📊 TEST SUMMARY');
  console.log('='.repeat(80));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`);

  if (testResults.failed > 0) {
    console.log('\n❌ Failed Tests:');
    testResults.tests.filter(t => !t.passed).forEach(test => {
      console.log(`   • ${test.name}: ${test.details}`);
    });
  }

  // Recommendations
  console.log('\n📋 RECOMMENDATIONS:');
  if (testResults.failed === 0) {
    console.log('🎉 All tests passed! The Compliance Calendar module is working correctly.');
  } else {
    console.log('🔧 Issues found that need attention:');
    testResults.tests.filter(t => !t.passed).forEach(test => {
      console.log(`   • Fix ${test.name}`);
    });
  }
}

// Run the tests
runComprehensiveTests().catch(console.error);
