-- =============================================
-- Client Reminders Stored Procedures
-- CA Portal - Multi-tenant Client Reminder System
-- Database: CA_Portal_kumar_associates
-- =============================================

USE [CA_Portal_kumar_associates];

PRINT '============================================================';
PRINT 'Creating Client Reminders Stored Procedures';
PRINT '============================================================';

-- =============================================
-- 1. sp_CreateClientReminder - Create new reminder
-- =============================================
PRINT '';
PRINT '1. CREATING sp_CreateClientReminder';
PRINT '-----------------------------------';

CREATE OR ALTER PROCEDURE sp_CreateClientReminder
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ReminderType NVARCHAR(50);
    DECLARE @TaxType NVARCHAR(100);
    DECLARE @DueDate DATETIME;
    DECLARE @ClientGroup NVARCHAR(100);
    DECLARE @CustomMessage NVARCHAR(MAX);
    DECLARE @CreatedBy UNIQUEIDENTIFIER;
    DECLARE @TenantId UNIQUEIDENTIFIER;
    DECLARE @ScheduledAt DATETIME;
    DECLARE @Priority NVARCHAR(20);
    DECLARE @Notes NVARCHAR(MAX);
    
    -- Parse JSON payload
    SELECT 
        @ReminderType = JSON_VALUE(@JsonPayload, '$.ReminderType'),
        @TaxType = JSON_VALUE(@JsonPayload, '$.TaxType'),
        @DueDate = CAST(JSON_VALUE(@JsonPayload, '$.DueDate') AS DATETIME),
        @ClientGroup = JSON_VALUE(@JsonPayload, '$.ClientGroup'),
        @CustomMessage = JSON_VALUE(@JsonPayload, '$.CustomMessage'),
        @CreatedBy = CAST(JSON_VALUE(@JsonPayload, '$.CreatedBy') AS UNIQUEIDENTIFIER),
        @TenantId = CAST(JSON_VALUE(@JsonPayload, '$.TenantId') AS UNIQUEIDENTIFIER),
        @ScheduledAt = CAST(JSON_VALUE(@JsonPayload, '$.ScheduledAt') AS DATETIME),
        @Priority = ISNULL(JSON_VALUE(@JsonPayload, '$.Priority'), 'Medium'),
        @Notes = JSON_VALUE(@JsonPayload, '$.Notes');
    
    -- Validate required fields
    IF @ReminderType IS NULL OR @TaxType IS NULL OR @DueDate IS NULL OR @ClientGroup IS NULL OR @CreatedBy IS NULL OR @TenantId IS NULL
    BEGIN
        THROW 50001, 'Missing required fields: ReminderType, TaxType, DueDate, ClientGroup, CreatedBy, TenantId', 1;
        RETURN;
    END
    
    -- Calculate client count based on group
    DECLARE @ClientCount INT = 0;
    
    IF @ClientGroup = 'All'
        SELECT @ClientCount = COUNT(*) FROM Clients WHERE IsActive = 1;
    ELSE IF @ClientGroup = 'Corporate'
        SELECT @ClientCount = COUNT(*) FROM Clients WHERE IsActive = 1 AND ClientType = 'Corporate';
    ELSE IF @ClientGroup = 'Individual'
        SELECT @ClientCount = COUNT(*) FROM Clients WHERE IsActive = 1 AND ClientType = 'Individual';
    ELSE IF @ClientGroup = 'MSME'
        SELECT @ClientCount = COUNT(*) FROM Clients WHERE IsActive = 1 AND BusinessType = 'MSME';
    ELSE IF @ClientGroup = 'High-Value'
        SELECT @ClientCount = COUNT(*) FROM Clients WHERE IsActive = 1 AND IsHighValue = 1;
    
    -- Create the reminder
    DECLARE @ReminderId UNIQUEIDENTIFIER = NEWID();
    
    INSERT INTO ClientReminders (
        ReminderId, ReminderType, TaxType, DueDate, ClientGroup, 
        CustomMessage, CreatedBy, TenantId, ScheduledAt, Priority, 
        Notes, ClientCount
    )
    VALUES (
        @ReminderId, @ReminderType, @TaxType, @DueDate, @ClientGroup,
        @CustomMessage, @CreatedBy, @TenantId, @ScheduledAt, @Priority,
        @Notes, @ClientCount
    );
    
    -- Return the created reminder
    SELECT 
        cr.ReminderId,
        cr.ReminderType,
        cr.TaxType,
        cr.DueDate,
        cr.ClientGroup,
        cr.CustomMessage,
        cr.Status,
        cr.ClientCount,
        cr.CreatedAt,
        cr.ScheduledAt,
        cr.SentAt,
        cr.Priority,
        cr.Notes,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unknown') AS CreatedByName
    FROM ClientReminders cr
    LEFT JOIN Users u ON cr.CreatedBy = u.UserId
    WHERE cr.ReminderId = @ReminderId
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END

PRINT '✅ sp_CreateClientReminder created';

-- =============================================
-- 2. sp_GetClientReminders - Get reminders with pagination
-- =============================================
PRINT '';
PRINT '2. CREATING sp_GetClientReminders';
PRINT '---------------------------------';

CREATE OR ALTER PROCEDURE sp_GetClientReminders
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @PageNumber INT;
    DECLARE @PageSize INT;
    DECLARE @Status NVARCHAR(50);
    DECLARE @TenantId UNIQUEIDENTIFIER;
    DECLARE @ClientGroup NVARCHAR(100);
    DECLARE @FromDate DATETIME;
    DECLARE @ToDate DATETIME;
    
    -- Parse JSON payload
    SELECT 
        @PageNumber = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.PageNumber') AS INT), 1),
        @PageSize = ISNULL(CAST(JSON_VALUE(@JsonPayload, '$.PageSize') AS INT), 20),
        @Status = JSON_VALUE(@JsonPayload, '$.Status'),
        @TenantId = CAST(JSON_VALUE(@JsonPayload, '$.TenantId') AS UNIQUEIDENTIFIER),
        @ClientGroup = JSON_VALUE(@JsonPayload, '$.ClientGroup'),
        @FromDate = CAST(JSON_VALUE(@JsonPayload, '$.FromDate') AS DATETIME),
        @ToDate = CAST(JSON_VALUE(@JsonPayload, '$.ToDate') AS DATETIME);
    
    -- Validate tenant
    IF @TenantId IS NULL
    BEGIN
        THROW 50001, 'TenantId is required', 1;
        RETURN;
    END
    
    -- Calculate offset
    DECLARE @Offset INT = (@PageNumber - 1) * @PageSize;
    
    -- Get total count
    DECLARE @TotalCount INT;
    SELECT @TotalCount = COUNT(*)
    FROM ClientReminders cr
    WHERE cr.TenantId = @TenantId
        AND cr.IsActive = 1
        AND (@Status IS NULL OR cr.Status = @Status)
        AND (@ClientGroup IS NULL OR cr.ClientGroup = @ClientGroup)
        AND (@FromDate IS NULL OR cr.CreatedAt >= @FromDate)
        AND (@ToDate IS NULL OR cr.CreatedAt <= @ToDate);
    
    -- Get reminders with pagination
    SELECT 
        cr.ReminderId,
        cr.ReminderType,
        cr.TaxType,
        cr.DueDate,
        cr.ClientGroup,
        cr.CustomMessage,
        cr.Status,
        cr.ClientCount,
        cr.CreatedAt,
        cr.ScheduledAt,
        cr.SentAt,
        cr.Priority,
        cr.Notes,
        cr.RetryCount,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unknown') AS CreatedByName,
        @TotalCount AS TotalCount,
        @PageNumber AS PageNumber,
        @PageSize AS PageSize,
        CEILING(CAST(@TotalCount AS FLOAT) / @PageSize) AS TotalPages
    FROM ClientReminders cr
    LEFT JOIN Users u ON cr.CreatedBy = u.UserId
    WHERE cr.TenantId = @TenantId
        AND cr.IsActive = 1
        AND (@Status IS NULL OR cr.Status = @Status)
        AND (@ClientGroup IS NULL OR cr.ClientGroup = @ClientGroup)
        AND (@FromDate IS NULL OR cr.CreatedAt >= @FromDate)
        AND (@ToDate IS NULL OR cr.CreatedAt <= @ToDate)
    ORDER BY cr.CreatedAt DESC
    OFFSET @Offset ROWS
    FETCH NEXT @PageSize ROWS ONLY
    FOR JSON PATH;
END

PRINT '✅ sp_GetClientReminders created';

-- =============================================
-- 3. sp_GetClientReminderById - Get single reminder
-- =============================================
PRINT '';
PRINT '3. CREATING sp_GetClientReminderById';
PRINT '------------------------------------';

CREATE OR ALTER PROCEDURE sp_GetClientReminderById
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ReminderId UNIQUEIDENTIFIER;
    DECLARE @TenantId UNIQUEIDENTIFIER;
    
    -- Parse JSON payload
    SELECT 
        @ReminderId = CAST(JSON_VALUE(@JsonPayload, '$.ReminderId') AS UNIQUEIDENTIFIER),
        @TenantId = CAST(JSON_VALUE(@JsonPayload, '$.TenantId') AS UNIQUEIDENTIFIER);
    
    -- Validate required fields
    IF @ReminderId IS NULL OR @TenantId IS NULL
    BEGIN
        THROW 50001, 'ReminderId and TenantId are required', 1;
        RETURN;
    END
    
    -- Get reminder details
    SELECT 
        cr.ReminderId,
        cr.ReminderType,
        cr.TaxType,
        cr.DueDate,
        cr.ClientGroup,
        cr.CustomMessage,
        cr.Status,
        cr.ClientCount,
        cr.CreatedAt,
        cr.ScheduledAt,
        cr.SentAt,
        cr.Priority,
        cr.Notes,
        cr.RetryCount,
        cr.LastRetryAt,
        ISNULL(u.FirstName + ' ' + u.LastName, 'Unknown') AS CreatedByName,
        ISNULL(u2.FirstName + ' ' + u2.LastName, '') AS UpdatedByName
    FROM ClientReminders cr
    LEFT JOIN Users u ON cr.CreatedBy = u.UserId
    LEFT JOIN Users u2 ON cr.UpdatedBy = u2.UserId
    WHERE cr.ReminderId = @ReminderId 
        AND cr.TenantId = @TenantId
        AND cr.IsActive = 1
    FOR JSON PATH, WITHOUT_ARRAY_WRAPPER;
END

PRINT '✅ sp_GetClientReminderById created';

-- =============================================
-- 4. sp_UpdateClientReminderStatus - Update reminder status
-- =============================================
PRINT '';
PRINT '4. CREATING sp_UpdateClientReminderStatus';
PRINT '-----------------------------------------';

CREATE OR ALTER PROCEDURE sp_UpdateClientReminderStatus
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @ReminderId UNIQUEIDENTIFIER;
    DECLARE @Status NVARCHAR(50);
    DECLARE @UpdatedBy UNIQUEIDENTIFIER;
    DECLARE @TenantId UNIQUEIDENTIFIER;
    DECLARE @Notes NVARCHAR(MAX);
    
    -- Parse JSON payload
    SELECT 
        @ReminderId = CAST(JSON_VALUE(@JsonPayload, '$.ReminderId') AS UNIQUEIDENTIFIER),
        @Status = JSON_VALUE(@JsonPayload, '$.Status'),
        @UpdatedBy = CAST(JSON_VALUE(@JsonPayload, '$.UpdatedBy') AS UNIQUEIDENTIFIER),
        @TenantId = CAST(JSON_VALUE(@JsonPayload, '$.TenantId') AS UNIQUEIDENTIFIER),
        @Notes = JSON_VALUE(@JsonPayload, '$.Notes');
    
    -- Validate required fields
    IF @ReminderId IS NULL OR @Status IS NULL OR @UpdatedBy IS NULL OR @TenantId IS NULL
    BEGIN
        THROW 50001, 'ReminderId, Status, UpdatedBy, and TenantId are required', 1;
        RETURN;
    END
    
    -- Check if reminder exists and belongs to tenant
    IF NOT EXISTS (SELECT 1 FROM ClientReminders WHERE ReminderId = @ReminderId AND TenantId = @TenantId AND IsActive = 1)
    BEGIN
        THROW 50002, 'Reminder not found or access denied', 1;
        RETURN;
    END
    
    -- Update the reminder
    UPDATE ClientReminders 
    SET 
        Status = @Status,
        UpdatedBy = @UpdatedBy,
        UpdatedAt = GETUTCDATE(),
        SentAt = CASE WHEN @Status = 'Sent' AND SentAt IS NULL THEN GETUTCDATE() ELSE SentAt END,
        Notes = ISNULL(@Notes, Notes)
    WHERE ReminderId = @ReminderId 
        AND TenantId = @TenantId;
    
    -- Return success indicator
    SELECT 1 AS Success, 'Reminder status updated successfully' AS Message;
END

PRINT '✅ sp_UpdateClientReminderStatus created';

-- =============================================
-- 5. sp_GetClientsForReminder - Get clients for specific group
-- =============================================
PRINT '';
PRINT '5. CREATING sp_GetClientsForReminder';
PRINT '------------------------------------';

CREATE OR ALTER PROCEDURE sp_GetClientsForReminder
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ClientGroup NVARCHAR(100);
    DECLARE @TenantId UNIQUEIDENTIFIER;

    -- Parse JSON payload
    SELECT
        @ClientGroup = JSON_VALUE(@JsonPayload, '$.ClientGroup'),
        @TenantId = CAST(JSON_VALUE(@JsonPayload, '$.TenantId') AS UNIQUEIDENTIFIER);

    -- Validate required fields
    IF @ClientGroup IS NULL OR @TenantId IS NULL
    BEGIN
        THROW 50001, 'ClientGroup and TenantId are required', 1;
        RETURN;
    END

    -- Get clients based on group criteria
    IF @ClientGroup = 'All'
    BEGIN
        SELECT
            c.ClientId,
            c.ClientCode,
            c.CompanyName,
            c.ContactPerson,
            c.Email,
            c.Phone,
            c.ClientType,
            c.BusinessType,
            c.IsHighValue
        FROM Clients c
        WHERE c.IsActive = 1
        ORDER BY c.CompanyName
        FOR JSON PATH;
    END
    ELSE IF @ClientGroup = 'Corporate'
    BEGIN
        SELECT
            c.ClientId,
            c.ClientCode,
            c.CompanyName,
            c.ContactPerson,
            c.Email,
            c.Phone,
            c.ClientType,
            c.BusinessType,
            c.IsHighValue
        FROM Clients c
        WHERE c.IsActive = 1 AND c.ClientType = 'Corporate'
        ORDER BY c.CompanyName
        FOR JSON PATH;
    END
    ELSE IF @ClientGroup = 'Individual'
    BEGIN
        SELECT
            c.ClientId,
            c.ClientCode,
            c.CompanyName,
            c.ContactPerson,
            c.Email,
            c.Phone,
            c.ClientType,
            c.BusinessType,
            c.IsHighValue
        FROM Clients c
        WHERE c.IsActive = 1 AND c.ClientType = 'Individual'
        ORDER BY c.CompanyName
        FOR JSON PATH;
    END
    ELSE IF @ClientGroup = 'MSME'
    BEGIN
        SELECT
            c.ClientId,
            c.ClientCode,
            c.CompanyName,
            c.ContactPerson,
            c.Email,
            c.Phone,
            c.ClientType,
            c.BusinessType,
            c.IsHighValue
        FROM Clients c
        WHERE c.IsActive = 1 AND c.BusinessType = 'MSME'
        ORDER BY c.CompanyName
        FOR JSON PATH;
    END
    ELSE IF @ClientGroup = 'High-Value'
    BEGIN
        SELECT
            c.ClientId,
            c.ClientCode,
            c.CompanyName,
            c.ContactPerson,
            c.Email,
            c.Phone,
            c.ClientType,
            c.BusinessType,
            c.IsHighValue
        FROM Clients c
        WHERE c.IsActive = 1 AND c.IsHighValue = 1
        ORDER BY c.CompanyName
        FOR JSON PATH;
    END
    ELSE
    BEGIN
        -- Return empty result for unknown groups
        SELECT
            c.ClientId,
            c.ClientCode,
            c.CompanyName,
            c.ContactPerson,
            c.Email,
            c.Phone,
            c.ClientType,
            c.BusinessType,
            c.IsHighValue
        FROM Clients c
        WHERE 1 = 0
        FOR JSON PATH;
    END
END

PRINT '✅ sp_GetClientsForReminder created';

-- =============================================
-- 6. sp_ScheduleClientReminder - Schedule reminder for sending
-- =============================================
PRINT '';
PRINT '6. CREATING sp_ScheduleClientReminder';
PRINT '-------------------------------------';

CREATE OR ALTER PROCEDURE sp_ScheduleClientReminder
    @JsonPayload NVARCHAR(MAX)
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @ReminderId UNIQUEIDENTIFIER;
    DECLARE @TenantId UNIQUEIDENTIFIER;
    DECLARE @UpdatedBy UNIQUEIDENTIFIER;

    -- Parse JSON payload
    SELECT
        @ReminderId = CAST(JSON_VALUE(@JsonPayload, '$.ReminderId') AS UNIQUEIDENTIFIER),
        @TenantId = CAST(JSON_VALUE(@JsonPayload, '$.TenantId') AS UNIQUEIDENTIFIER),
        @UpdatedBy = CAST(JSON_VALUE(@JsonPayload, '$.UpdatedBy') AS UNIQUEIDENTIFIER);

    -- Validate required fields
    IF @ReminderId IS NULL OR @TenantId IS NULL OR @UpdatedBy IS NULL
    BEGIN
        THROW 50001, 'ReminderId, TenantId, and UpdatedBy are required', 1;
        RETURN;
    END

    -- Check if reminder exists and is in correct status
    IF NOT EXISTS (
        SELECT 1 FROM ClientReminders
        WHERE ReminderId = @ReminderId
            AND TenantId = @TenantId
            AND IsActive = 1
            AND Status IN ('Scheduled', 'Failed')
    )
    BEGIN
        THROW 50002, 'Reminder not found or cannot be scheduled', 1;
        RETURN;
    END

    BEGIN TRANSACTION;

    TRY
        -- Get reminder details
        DECLARE @ReminderType NVARCHAR(50);
        DECLARE @ClientGroup NVARCHAR(100);

        SELECT @ReminderType = ReminderType, @ClientGroup = ClientGroup
        FROM ClientReminders
        WHERE ReminderId = @ReminderId;

        -- Clear existing delivery records
        DELETE FROM ClientReminderDelivery WHERE ReminderId = @ReminderId;

        -- Create delivery records for each client in the group
        INSERT INTO ClientReminderDelivery (
            ReminderId, ClientId, DeliveryType, ContactEmail, ContactPhone, ContactName
        )
        SELECT
            @ReminderId,
            c.ClientId,
            CASE
                WHEN @ReminderType = 'Email' THEN 'Email'
                WHEN @ReminderType = 'WhatsApp' THEN 'WhatsApp'
                ELSE 'Email' -- Default to Email for 'Both' - will create WhatsApp records separately
            END,
            c.Email,
            c.Phone,
            ISNULL(c.ContactPerson, c.CompanyName)
        FROM Clients c
        WHERE c.IsActive = 1
            AND (
                (@ClientGroup = 'All') OR
                (@ClientGroup = 'Corporate' AND c.ClientType = 'Corporate') OR
                (@ClientGroup = 'Individual' AND c.ClientType = 'Individual') OR
                (@ClientGroup = 'MSME' AND c.BusinessType = 'MSME') OR
                (@ClientGroup = 'High-Value' AND c.IsHighValue = 1)
            );

        -- If reminder type is 'Both', create WhatsApp delivery records too
        IF @ReminderType = 'Both'
        BEGIN
            INSERT INTO ClientReminderDelivery (
                ReminderId, ClientId, DeliveryType, ContactEmail, ContactPhone, ContactName
            )
            SELECT
                @ReminderId,
                c.ClientId,
                'WhatsApp',
                c.Email,
                c.Phone,
                ISNULL(c.ContactPerson, c.CompanyName)
            FROM Clients c
            WHERE c.IsActive = 1
                AND (
                    (@ClientGroup = 'All') OR
                    (@ClientGroup = 'Corporate' AND c.ClientType = 'Corporate') OR
                    (@ClientGroup = 'Individual' AND c.ClientType = 'Individual') OR
                    (@ClientGroup = 'MSME' AND c.BusinessType = 'MSME') OR
                    (@ClientGroup = 'High-Value' AND c.IsHighValue = 1)
                );
        END

        -- Update reminder status to Processing
        UPDATE ClientReminders
        SET
            Status = 'Processing',
            UpdatedBy = @UpdatedBy,
            UpdatedAt = GETUTCDATE()
        WHERE ReminderId = @ReminderId;

        -- Get count of delivery records created
        DECLARE @DeliveryCount INT;
        SELECT @DeliveryCount = COUNT(*) FROM ClientReminderDelivery WHERE ReminderId = @ReminderId;

        COMMIT TRANSACTION;

        -- Return success with delivery count
        SELECT 1 AS Success, 'Reminder scheduled successfully' AS Message, @DeliveryCount AS DeliveryCount;

    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END

PRINT '✅ sp_ScheduleClientReminder created';

PRINT '';
PRINT '============================================================';
PRINT 'Client Reminders Stored Procedures Creation Complete!';
PRINT 'Next: Create backend API controllers and services';
PRINT '============================================================';
