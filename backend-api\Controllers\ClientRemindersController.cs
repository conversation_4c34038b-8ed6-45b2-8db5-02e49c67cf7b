using CAPortalAPI.Models.DTOs;
using CAPortalAPI.Services;
using CAPortalAPI.Models.Common;
using CAPortalAPI.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Linq;

namespace CAPortalAPI.Controllers
{
    /// <summary>
    /// Controller for managing client reminders
    /// Handles CRUD operations and scheduling for client reminders
    /// </summary>
    [ApiController]
    [Route("api/client-reminders")]
    [Authorize]
    public class ClientRemindersController : ControllerBase
    {
        private readonly IClientReminderService? _reminderService;
        private readonly ILogger<ClientRemindersController> _logger;

        public ClientRemindersController(
            ILogger<ClientRemindersController> logger,
            IClientReminderService? reminderService = null)
        {
            _reminderService = reminderService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new client reminder
        /// </summary>
        /// <param name="request">Reminder creation request</param>
        /// <returns>Created reminder details</returns>
        [HttpPost]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> CreateReminder([FromBody] CreateClientReminderRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("User context or tenant information is missing"));
                }

                _logger.LogInformation("Creating client reminder for user {UserId}, tenant {TenantKey}", userId, tenantKey);

                var result = await _reminderService.CreateReminderAsync(request, userId, tenantKey);
                
                return Ok(ApiResponse<ClientReminderDto>.SuccessResponse(result, "Client reminder created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating client reminder");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to create client reminder"));
            }
        }

        /// <summary>
        /// Gets paginated list of client reminders with optional filters
        /// </summary>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 20)</param>
        /// <param name="status">Optional status filter</param>
        /// <param name="clientGroup">Optional client group filter</param>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <returns>Paginated reminder list</returns>
        [HttpGet]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetReminders(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? status = null,
            [FromQuery] string? clientGroup = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                var request = new GetClientRemindersRequestDto
                {
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    Status = status,
                    ClientGroup = clientGroup,
                    FromDate = fromDate,
                    ToDate = toDate
                };

                _logger.LogInformation("Getting client reminders for tenant {TenantKey}, page {PageNumber}", tenantKey, pageNumber);

                var result = await _reminderService.GetRemindersAsync(request, tenantKey);
                
                return Ok(ApiResponse<GetClientRemindersResponseDto>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client reminders");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get client reminders"));
            }
        }

        /// <summary>
        /// Gets a specific client reminder by ID
        /// </summary>
        /// <param name="id">Reminder ID</param>
        /// <returns>Reminder details</returns>
        [HttpGet("{id}")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetReminder(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                _logger.LogInformation("Getting client reminder {ReminderId} for tenant {TenantKey}", id, tenantKey);

                var result = await _reminderService.GetReminderByIdAsync(id, tenantKey);
                
                if (result == null)
                {
                    return NotFound(ApiResponse.ErrorResponse("Reminder not found"));
                }

                return Ok(ApiResponse<ClientReminderDto>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client reminder {ReminderId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get client reminder"));
            }
        }

        /// <summary>
        /// Updates the status of a client reminder
        /// </summary>
        /// <param name="id">Reminder ID</param>
        /// <param name="request">Status update request</param>
        /// <returns>Success response</returns>
        [HttpPut("{id}/status")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> UpdateReminderStatus(Guid id, [FromBody] UpdateClientReminderStatusDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("User context or tenant information is missing"));
                }

                _logger.LogInformation("Updating client reminder {ReminderId} status to {Status} for tenant {TenantKey}", 
                    id, request.Status, tenantKey);

                var result = await _reminderService.UpdateReminderStatusAsync(id, request, userId, tenantKey);
                
                if (!result)
                {
                    return NotFound(ApiResponse.ErrorResponse("Reminder not found or update failed"));
                }

                return Ok(ApiResponse.SuccessResponse("Reminder status updated successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating client reminder {ReminderId} status", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to update reminder status"));
            }
        }

        /// <summary>
        /// Schedules a reminder for sending
        /// </summary>
        /// <param name="id">Reminder ID</param>
        /// <returns>Scheduling result</returns>
        [HttpPost("{id}/schedule")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> ScheduleReminder(Guid id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("User context or tenant information is missing"));
                }

                _logger.LogInformation("Scheduling client reminder {ReminderId} for tenant {TenantKey}", id, tenantKey);

                var result = await _reminderService.ScheduleReminderAsync(id, userId, tenantKey);
                
                if (!result.Success)
                {
                    return BadRequest(ApiResponse.ErrorResponse(result.Message));
                }

                return Ok(ApiResponse<ScheduleReminderResponseDto>.SuccessResponse(result, "Reminder scheduled successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error scheduling client reminder {ReminderId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to schedule reminder"));
            }
        }

        /// <summary>
        /// Cancels a client reminder
        /// </summary>
        /// <param name="id">Reminder ID</param>
        /// <returns>Success response</returns>
        [HttpDelete("{id}")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> CancelReminder(Guid id)
        {
            try
            {
                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("User context or tenant information is missing"));
                }

                _logger.LogInformation("Cancelling client reminder {ReminderId} for tenant {TenantKey}", id, tenantKey);

                var result = await _reminderService.CancelReminderAsync(id, userId, tenantKey);
                
                if (!result)
                {
                    return NotFound(ApiResponse.ErrorResponse("Reminder not found or cancellation failed"));
                }

                return Ok(ApiResponse.SuccessResponse("Reminder cancelled successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling client reminder {ReminderId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to cancel reminder"));
            }
        }

        /// <summary>
        /// Gets list of clients for a specific client group
        /// </summary>
        /// <param name="group">Client group name</param>
        /// <returns>List of clients in the group</returns>
        [HttpGet("client-groups/{group}/clients")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetClientsForGroup(string group)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                _logger.LogInformation("Getting clients for group {ClientGroup} for tenant {TenantKey}", group, tenantKey);

                var result = await _reminderService.GetClientsForGroupAsync(group, tenantKey);
                
                return Ok(ApiResponse<List<ClientForReminderDto>>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting clients for group {ClientGroup}", group);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get clients for group"));
            }
        }

        /// <summary>
        /// Gets available client groups
        /// </summary>
        /// <returns>List of available client groups</returns>
        [HttpGet("client-groups")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetClientGroups()
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                _logger.LogInformation("Getting client groups for tenant {TenantKey}", tenantKey);

                var result = await _reminderService.GetClientGroupsAsync(tenantKey);

                return Ok(ApiResponse<List<ClientGroupDto>>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client groups");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get client groups"));
            }
        }

        /// <summary>
        /// Gets predefined reminder templates
        /// </summary>
        /// <param name="taxType">Optional tax type filter</param>
        /// <returns>List of reminder templates</returns>
        [HttpGet("templates")]
        [AllowAnonymous] // Temporarily disable auth for testing
        public async Task<IActionResult> GetReminderTemplates([FromQuery] string? taxType = null)
        {
            try
            {
                _logger.LogInformation("Getting reminder templates for tax type {TaxType}", taxType ?? "All");

                var result = await _reminderService.GetReminderTemplatesAsync(taxType);

                return Ok(ApiResponse<List<ReminderTemplateDto>>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder templates");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get reminder templates"));
            }
        }

        /// <summary>
        /// Simple test endpoint to verify controller is working
        /// </summary>
        /// <returns>Test response</returns>
        [HttpGet("test")]
        [AllowAnonymous]
        public IActionResult Test()
        {
            return Ok(new { message = "ClientReminders controller is working!", timestamp = DateTime.UtcNow });
        }

        /// <summary>
        /// Gets reminder statistics for dashboard
        /// </summary>
        /// <param name="fromDate">Optional start date for statistics</param>
        /// <param name="toDate">Optional end date for statistics</param>
        /// <returns>Reminder statistics</returns>
        [HttpGet("stats")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetReminderStats(
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                _logger.LogInformation("Getting reminder statistics for tenant {TenantKey}", tenantKey);

                var result = await _reminderService.GetReminderStatsAsync(tenantKey, fromDate, toDate);

                return Ok(ApiResponse<ClientReminderStatsDto>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder statistics");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get reminder statistics"));
            }
        }

        /// <summary>
        /// Gets delivery status for a specific reminder
        /// </summary>
        /// <param name="id">Reminder ID</param>
        /// <returns>List of delivery records</returns>
        [HttpGet("{id}/delivery-status")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetReminderDeliveryStatus(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                _logger.LogInformation("Getting delivery status for reminder {ReminderId} for tenant {TenantKey}", id, tenantKey);

                var result = await _reminderService.GetReminderDeliveryStatusAsync(id, tenantKey);

                return Ok(ApiResponse<List<ClientReminderDeliveryDto>>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting delivery status for reminder {ReminderId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get delivery status"));
            }
        }

        /// <summary>
        /// Gets delivery summary for a reminder
        /// </summary>
        /// <param name="id">Reminder ID</param>
        /// <returns>Delivery summary statistics</returns>
        [HttpGet("{id}/delivery-summary")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetReminderDeliverySummary(Guid id)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                _logger.LogInformation("Getting delivery summary for reminder {ReminderId} for tenant {TenantKey}", id, tenantKey);

                var result = await _reminderService.GetReminderDeliverySummaryAsync(id, tenantKey);

                if (result == null)
                {
                    return NotFound(ApiResponse.ErrorResponse("Reminder not found"));
                }

                return Ok(ApiResponse<ReminderDeliverySummaryDto>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting delivery summary for reminder {ReminderId}", id);
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get delivery summary"));
            }
        }

        /// <summary>
        /// Performs bulk operations on multiple reminders
        /// </summary>
        /// <param name="request">Bulk operation request</param>
        /// <returns>Number of reminders affected</returns>
        [HttpPost("bulk-operation")]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> BulkReminderOperation([FromBody] BulkReminderOperationDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("User context or tenant information is missing"));
                }

                _logger.LogInformation("Performing bulk operation {Operation} on {Count} reminders for tenant {TenantKey}",
                    request.Operation, request.ReminderIds.Count, tenantKey);

                var result = await _reminderService.BulkReminderOperationAsync(request, userId, tenantKey);

                return Ok(ApiResponse<int>.SuccessResponse(result, $"Bulk operation completed. {result} reminders affected."));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error performing bulk reminder operation");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to perform bulk operation"));
            }
        }

        /// <summary>
        /// Gets the current user ID from JWT claims
        /// </summary>
        /// <returns>Current user ID or empty string if not found</returns>
        private string GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return userIdClaim ?? "";
        }

        /// <summary>
        /// Gets the current tenant key from JWT claims
        /// </summary>
        /// <returns>Current tenant key or empty string if not found</returns>
        private string GetCurrentTenantKey()
        {
            return User.FindFirst("TenantKey")?.Value ?? "";
        }
    }
}
