using CAPortalAPI.Models.DTOs;
using CAPortalAPI.Services;
using CAPortalAPI.Models.Common;
using CAPortalAPI.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Linq;

namespace CAPortalAPI.Controllers
{
    /// <summary>
    /// Controller for managing client reminders
    /// Handles CRUD operations and scheduling for client reminders
    /// </summary>
    [ApiController]
    [Route("api/client-reminders")]
    [Authorize]
    public class ClientRemindersController : ControllerBase
    {
        private readonly IClientReminderService _reminderService;
        private readonly ILogger<ClientRemindersController> _logger;

        public ClientRemindersController(
            IClientReminderService reminderService,
            ILogger<ClientRemindersController> logger)
        {
            _reminderService = reminderService;
            _logger = logger;
        }

        /// <summary>
        /// Simple test endpoint to verify controller is working
        /// </summary>
        /// <returns>Test response</returns>
        [HttpGet("test")]
        [AllowAnonymous]
        public IActionResult Test()
        {
            return Ok(new { message = "ClientReminders controller is working!", timestamp = DateTime.UtcNow });
        }

        /// <summary>
        /// Creates a new client reminder
        /// </summary>
        /// <param name="request">Reminder creation request</param>
        /// <returns>Created reminder details</returns>
        [HttpPost]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> CreateReminder([FromBody] CreateClientReminderRequestDto request)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ApiResponse.ErrorResponse("Invalid request data",
                        ModelState.Values.SelectMany(v => v.Errors.Select(e => e.ErrorMessage)).ToList()));
                }

                var userId = GetCurrentUserId();
                var tenantKey = GetCurrentTenantKey();

                if (string.IsNullOrEmpty(userId) || string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("User context or tenant information is missing"));
                }

                _logger.LogInformation("Creating client reminder for user {UserId}, tenant {TenantKey}", userId, tenantKey);

                var result = await _reminderService.CreateReminderAsync(request, userId, tenantKey);
                
                return Ok(ApiResponse<ClientReminderDto>.SuccessResponse(result, "Client reminder created successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating client reminder");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to create client reminder"));
            }
        }

        /// <summary>
        /// Gets paginated list of client reminders with optional filters
        /// </summary>
        /// <param name="pageNumber">Page number (default: 1)</param>
        /// <param name="pageSize">Page size (default: 20)</param>
        /// <param name="status">Optional status filter</param>
        /// <param name="clientGroup">Optional client group filter</param>
        /// <param name="fromDate">Optional start date filter</param>
        /// <param name="toDate">Optional end date filter</param>
        /// <returns>Paginated reminder list</returns>
        [HttpGet]
        [Authorize(Policy = AuthorizationPolicies.AllStaff)]
        public async Task<IActionResult> GetReminders(
            [FromQuery] int pageNumber = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? status = null,
            [FromQuery] string? clientGroup = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var tenantKey = GetCurrentTenantKey();
                if (string.IsNullOrEmpty(tenantKey))
                {
                    return BadRequest(ApiResponse.ErrorResponse("Tenant context required"));
                }

                var request = new GetClientRemindersRequestDto
                {
                    PageNumber = pageNumber,
                    PageSize = pageSize,
                    Status = status,
                    ClientGroup = clientGroup,
                    FromDate = fromDate,
                    ToDate = toDate
                };

                _logger.LogInformation("Getting client reminders for tenant {TenantKey}, page {PageNumber}", tenantKey, pageNumber);

                var result = await _reminderService.GetRemindersAsync(request, tenantKey);
                
                return Ok(ApiResponse<GetClientRemindersResponseDto>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting client reminders");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get client reminders"));
            }
        }

        /// <summary>
        /// Gets predefined reminder templates
        /// </summary>
        /// <param name="taxType">Optional tax type filter</param>
        /// <returns>List of reminder templates</returns>
        [HttpGet("templates")]
        [AllowAnonymous] // Temporarily disable auth for testing
        public async Task<IActionResult> GetReminderTemplates([FromQuery] string? taxType = null)
        {
            try
            {
                _logger.LogInformation("Getting reminder templates for tax type {TaxType}", taxType ?? "All");

                var result = await _reminderService.GetReminderTemplatesAsync(taxType);
                
                return Ok(ApiResponse<List<ReminderTemplateDto>>.SuccessResponse(result));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting reminder templates");
                return StatusCode(500, ApiResponse.ErrorResponse("Failed to get reminder templates"));
            }
        }

        /// <summary>
        /// Gets the current user ID from JWT claims
        /// </summary>
        /// <returns>Current user ID or empty string if not found</returns>
        private string GetCurrentUserId()
        {
            var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;
            return userIdClaim ?? "";
        }

        /// <summary>
        /// Gets the current tenant key from JWT claims
        /// </summary>
        /// <returns>Current tenant key or empty string if not found</returns>
        private string GetCurrentTenantKey()
        {
            return User.FindFirst("TenantKey")?.Value ?? "";
        }
    }
}
